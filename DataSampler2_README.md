# DataSampler2 多周期数据采样器

## 概述

DataSampler2 是一个专门设计用于处理多周期因子特征向量的数据采样器。它将多周期的因子特征向量独立处理，然后合并为统一的三维数组结构，同时保持代码、时间、标签的一致性，非常适合深度学习模型训练。

## 主要特点

### 1. 多周期独立处理与合并
- ✅ **独立处理**: 每个周期的因子特征向量独立处理，保持各自特点
- ✅ **统一合并**: 合并为统一的三维数组结构 `(样本数, 窗口大小, 总特征数)`
- ✅ **周期识别**: 周期根据 `fd_dfs` 的键来决定（如 'fd_1_0', 'fd_1_1', 'fd_2_0'）
- ✅ **特征维度灵活**: 不同周期可以有不同数量的特征

### 2. 数据一致性
- ✅ **统一时间索引**: 所有周期使用相同的时间索引进行采样
- ✅ **代码标签一致**: 代码、时间、标签在所有周期间保持一致
- ✅ **跨代码检查**: 确保采样窗口不跨越不同的交易代码
- ✅ **时间特征支持**: 支持时间编码特征

### 3. 采样模式
- ✅ **常规采样**: 按步长进行规律采样
- ✅ **过滤采样**: 基于滚动窗口最大值进行过滤采样
- ✅ **极端值过滤**: 可选的极端值过滤功能

### 4. 模型训练友好
- ✅ **三维数组结构**: 直接兼容深度学习框架（PyTorch/TensorFlow）
- ✅ **批量训练**: 支持批量训练和GPU加速
- ✅ **特征顺序固定**: 确保训练和推理时特征顺序一致

## 数据结构

### 输入数据格式

```python
# fd_dfs: 多周期因子数据字典
fd_dfs = {
    'fd_1_0': pd.DataFrame({  # 短期因子
        'code': [...],
        'date': [...],
        'change': [...],
        'RSI_2': [...],
        'MACD_2': [...],
        'VOL_2': [...]
    }),
    'fd_1_1': pd.DataFrame({  # 中期因子
        'code': [...],
        'date': [...],
        'change': [...],
        'MA_5_2': [...],
        'MA_10_2': [...],
        'BOLL_UP_2': [...],
        'BOLL_DOWN_2': [...]
    }),
    'fd_2_0': pd.DataFrame({  # 长期因子
        'code': [...],
        'date': [...],
        'change': [...],
        'MA_20_2': [...],
        'ATR_2': [...]
    })
}

# lb_df: 标签数据框
lb_df = pd.DataFrame({
    'code': [...],
    'date': [...],
    'change': [...],
    'long_label': [...],
    'short_label': [...],
    'label': [...],
    'code_encoded': [...],
    'tf0': [...],  # 时间特征
    'tf1': [...],
    'tf2': [...],
    'tf3': [...],
    'tf4': [...]
})
```

### 输出数据格式

```python
# 返回合并后的统一结果
result = (code_data, x_data, y_data, [x_mark, y_mark])

# 数据形状示例:
code_data.shape = (样本数, 窗口大小)           # 代码编码序列
x_data.shape = (样本数, 窗口大小, 总特征数)      # 合并后的特征数据
y_data.shape = (样本数,)                     # 标签数据
x_mark = List[Array]                         # 时间特征序列（可选）
y_mark = List[Array]                         # 时间标签（可选）

# 特征分布示例（假设fd_1_0有5个特征，fd_1_1有6个特征，fd_2_0有3个特征）:
# x_data[:, :, 0:5]   -> fd_1_0的特征（短期技术指标）
# x_data[:, :, 5:11]  -> fd_1_1的特征（中期移动平均）
# x_data[:, :, 11:14] -> fd_2_0的特征（长期趋势指标）
```

## 使用方法

### 基本使用

```python
from pyqlab.data.dataset.handler import DataSampler2, DataConfig, DirectionType

# 1. 创建配置
config = DataConfig(
    win=10,                    # 窗口大小
    step=1,                    # 采样步长
    is_filter_extreme=False,   # 极端值过滤
    is_normal=False,           # 数据归一化
    verbose=True,              # 详细日志
    timeenc=0,                 # 时间编码
    extreme_threshold=3.0      # 极端值阈值
)

# 2. 创建采样器
sampler = DataSampler2(config)

# 3. 进行采样
result = sampler.sample_data(
    fd_dfs=fd_dfs,                    # 多周期因子数据
    lb_df=lb_df,                      # 标签数据
    direction=DirectionType.LONG_SHORT, # 交易方向
    win=10,                           # 窗口大小
    filter_win=0                      # 过滤窗口（0表示常规采样）
)

# 4. 处理结果
code_data, x_data, y_data = result[0], result[1], result[2]
print(f"样本数: {len(x_data)}")
print(f"特征维度: {x_data.shape}")  # (样本数, 窗口大小, 总特征数)
print(f"标签维度: {y_data.shape}")

# 5. 转换为深度学习框架格式
import torch
x_tensor = torch.FloatTensor(x_data)
y_tensor = torch.FloatTensor(y_data)
```

### 过滤采样

```python
# 使用过滤窗口进行采样
result_filtered = sampler.sample_data(
    fd_dfs=fd_dfs,
    lb_df=lb_df,
    direction=DirectionType.MULTI_LABEL,
    win=10,
    filter_win=5  # 使用5个时间步的过滤窗口
)

code_data_f, x_data_f, y_data_f = result_filtered[0], result_filtered[1], result_filtered[2]
print(f"过滤采样结果: {x_data_f.shape}")
```

## 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `win` | int | 10 | 采样窗口大小 |
| `step` | int | 1 | 采样步长 |
| `is_filter_extreme` | bool | False | 是否过滤极端值 |
| `is_normal` | bool | True | 是否进行数据归一化 |
| `verbose` | bool | False | 是否输出详细日志 |
| `timeenc` | int | None | 时间编码类型 |
| `extreme_threshold` | float | 3.0 | 极端值过滤阈值 |

## 交易方向类型

| 方向 | 枚举值 | 说明 |
|------|--------|------|
| 多头 | `DirectionType.LONG` | 使用 long_label |
| 空头 | `DirectionType.SHORT` | 使用 short_label |
| 多空 | `DirectionType.LONG_SHORT` | 使用 change 值 |
| 多分类 | `DirectionType.MULTI_LABEL` | 使用 label |

## 实际应用建议

### 1. 周期特征设计
- **短期周期 (fd_1_0)**: 技术指标、价格动量、成交量比率等
- **中期周期 (fd_1_1)**: 移动平均、布林带、趋势指标等  
- **长期周期 (fd_2_0)**: 长期移动平均、波动率、宏观指标等

### 2. 模型架构建议

#### 方案1: 统一LSTM处理所有特征
```python
class UnifiedLSTM(nn.Module):
    def __init__(self, total_features=14):  # 所有周期特征总数
        super().__init__()
        self.lstm = nn.LSTM(total_features, 64, num_layers=2, batch_first=True)
        self.fc = nn.Linear(64, 1)
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        # x shape: (batch_size, seq_len, total_features)
        lstm_out, _ = self.lstm(x)
        output = self.dropout(lstm_out[:, -1, :])  # 取最后时间步
        return self.fc(output)
```

#### 方案2: 分支处理不同周期特征
```python
class MultiPeriodBranchModel(nn.Module):
    def __init__(self):
        super().__init__()
        # 为不同周期设计不同的处理分支
        self.short_branch = nn.LSTM(5, 32)    # fd_1_0: 5个特征
        self.medium_branch = nn.LSTM(6, 32)   # fd_1_1: 6个特征
        self.long_branch = nn.LSTM(3, 32)     # fd_2_0: 3个特征
        self.fusion = nn.Linear(96, 1)

    def forward(self, x):
        # 分离不同周期的特征
        short_features = x[:, :, 0:5]      # fd_1_0特征
        medium_features = x[:, :, 5:11]    # fd_1_1特征
        long_features = x[:, :, 11:14]     # fd_2_0特征

        short_out, _ = self.short_branch(short_features)
        medium_out, _ = self.medium_branch(medium_features)
        long_out, _ = self.long_branch(long_features)

        # 取最后时间步并合并
        combined = torch.cat([
            short_out[:, -1, :],
            medium_out[:, -1, :],
            long_out[:, -1, :]
        ], dim=-1)

        return self.fusion(combined)
```

### 3. 数据质量检查
- 确保所有周期的数据在时间上对齐
- 检查不同周期的特征数量和类型
- 验证标签数据的完整性

## 与原 DataSampler 的区别

| 特性 | DataSampler | DataSampler2 |
|------|-------------|--------------|
| 数据输入 | 合并后的特征矩阵 | 多周期分离的数据字典 |
| 特征处理 | 所有特征合并为一个矩阵 | 每个周期独立处理后合并 |
| 输出格式 | 单一的特征张量 | 统一的三维数组结构 |
| 灵活性 | 固定的特征维度 | 每个周期可有不同特征数 |
| 数据一致性 | 基本一致性 | 强制代码、时间、标签一致 |
| 模型兼容性 | 传统机器学习模型 | 深度学习框架优化 |
| 特征可解释性 | 特征混合 | 周期特征可分离分析 |

## 测试和验证

项目包含完整的测试文件：
- `test_data_sampler2.py`: 基础功能测试
- `test_data_sampler2_merged.py`: 合并功能测试
- `example_data_sampler2_usage.py`: 基础使用示例
- `example_data_sampler2_merged_usage.py`: 合并功能使用示例

运行测试：
```bash
python test_data_sampler2.py
python test_data_sampler2_merged.py
python example_data_sampler2_merged_usage.py
```

## 总结

DataSampler2 为多周期因子数据处理提供了更灵活和强大的解决方案。它既保持了不同周期特征的独立性，又提供了统一的三维数组结构，完美平衡了特征可解释性和模型训练便利性。

### 核心优势
1. **多周期独立处理**: 保持各周期特征的独特性和可解释性
2. **统一数据结构**: 生成 `TF x PERIOD x Win` 三维数组，便于模型训练
3. **数据一致性保证**: 代码、时间、标签在所有周期间严格对齐
4. **深度学习优化**: 直接兼容PyTorch/TensorFlow等主流框架
5. **灵活的模型架构**: 支持统一处理和分支处理两种模型设计

DataSampler2 特别适合需要处理多时间尺度市场特征的量化交易模型，能够更好地捕捉短期技术指标、中期趋势和长期基本面信息。
