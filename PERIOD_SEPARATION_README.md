# 周期维度分离功能说明

## 概述

在 `DataHandler` 中新增了 `keep_period_separate` 配置选项，允许在 x_data 中维持"周期个数 x 周期向量个数"的二维结构，而不是将所有周期的特征合并到一个维度中。

## 功能特点

### 1. 两种数据结构模式

#### 默认模式 (keep_period_separate=False)
- **数据形状**: `(样本数, 窗口大小, 总特征数)`
- **特征排列**: 所有周期的特征按顺序合并在最后一个维度中
- **兼容性**: 与现有代码完全兼容
- **适用场景**: 传统的深度学习模型，如 LSTM、GRU、Transformer 等

#### 周期分离模式 (keep_period_separate=True)
- **数据形状**: `(样本数, 周期数, 窗口大小, 每周期特征数)`
- **特征排列**: 每个周期的特征保持独立的维度
- **优势**: 便于实现多尺度特征处理和周期间的注意力机制
- **适用场景**: 需要分别处理不同周期特征的高级模型

### 2. 配置方法

```python
from pyqlab.data.dataset.handler import DataHandler

# 默认模式
handler_default = DataHandler(
    data_loader=loader,
    keep_period_separate=False,  # 默认值
    # ... 其他参数
)

# 周期分离模式
handler_separate = DataHandler(
    data_loader=loader,
    keep_period_separate=True,   # 启用周期分离
    # ... 其他参数
)
```

## 数据结构对比

### 示例数据
假设有3个周期的数据：
- `fd_1_0`: 5个短期技术指标
- `fd_1_1`: 3个中期移动平均指标  
- `fd_2_0`: 2个长期趋势指标

### 默认模式输出
```python
x_data.shape = (1000, 10, 10)  # (样本数, 窗口大小, 总特征数)

# 特征分布:
# x_data[:, :, 0:5]   -> fd_1_0的5个特征
# x_data[:, :, 5:8]   -> fd_1_1的3个特征  
# x_data[:, :, 8:10]  -> fd_2_0的2个特征
```

### 周期分离模式输出
```python
x_data.shape = (1000, 3, 10, 5)  # (样本数, 周期数, 窗口大小, 最大特征数)

# 周期分布:
# x_data[:, 0, :, :5] -> fd_1_0的5个特征
# x_data[:, 1, :, :3] -> fd_1_1的3个特征 (后2个位置为0填充)
# x_data[:, 2, :, :2] -> fd_2_0的2个特征 (后3个位置为0填充)
```

## 使用场景

### 1. 传统深度学习模型
使用默认模式，直接将所有特征输入模型：

```python
# LSTM 示例
import torch.nn as nn

class LSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers):
        super().__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, 1)
    
    def forward(self, x):
        # x.shape: (batch_size, seq_len, input_size)
        out, _ = self.lstm(x)
        return self.fc(out[:, -1, :])

# 使用默认模式的数据
model = LSTMModel(input_size=10, hidden_size=64, num_layers=2)
```

### 2. 多尺度特征融合模型
使用周期分离模式，分别处理不同周期的特征：

```python
class MultiScaleModel(nn.Module):
    def __init__(self, period_configs):
        super().__init__()
        self.period_encoders = nn.ModuleList([
            nn.LSTM(config['input_size'], config['hidden_size'], batch_first=True)
            for config in period_configs
        ])
        self.fusion = nn.Linear(sum(config['hidden_size'] for config in period_configs), 1)
    
    def forward(self, x):
        # x.shape: (batch_size, num_periods, seq_len, max_features)
        period_outputs = []
        for i, encoder in enumerate(self.period_encoders):
            period_data = x[:, i, :, :period_configs[i]['input_size']]
            out, _ = encoder(period_data)
            period_outputs.append(out[:, -1, :])
        
        combined = torch.cat(period_outputs, dim=1)
        return self.fusion(combined)

# 使用周期分离模式的数据
period_configs = [
    {'input_size': 5, 'hidden_size': 32},  # fd_1_0
    {'input_size': 3, 'hidden_size': 24},  # fd_1_1
    {'input_size': 2, 'hidden_size': 16},  # fd_2_0
]
model = MultiScaleModel(period_configs)
```

## 实现细节

### 1. 内存优化
- 周期分离模式会使用零填充来统一特征维度
- 对于特征数差异较大的周期，可能会增加内存使用
- 建议在模型中使用 mask 来忽略填充的零值

### 2. 特征对齐
- 所有周期使用相同的时间索引进行采样
- 确保代码、时间、标签在所有周期间保持一致
- 采样窗口不会跨越不同的交易代码

### 3. 向后兼容
- 默认情况下 `keep_period_separate=False`，保持原有行为
- 现有代码无需修改即可正常工作
- 新功能是可选的增强功能

## 注意事项

1. **内存使用**: 周期分离模式可能会增加内存使用，特别是当周期间特征数差异较大时
2. **模型设计**: 使用周期分离模式时，需要相应地调整模型架构
3. **特征填充**: 较短的特征向量会用零填充到最大长度
4. **性能考虑**: 对于简单模型，默认模式通常更高效

## 示例代码

完整的使用示例请参考 `example_keep_period_separate.py` 文件。
