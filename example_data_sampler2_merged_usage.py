#!/usr/bin/env python3
"""
DataSampler2 合并功能使用示例
展示如何使用新的多周期数据采样器生成统一的三维数组结构
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn

def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")
    
    # 创建时间序列
    n_samples = 300
    dates = pd.date_range('2024-01-01', periods=n_samples, freq='H')
    timestamps = dates.astype(np.int64) // 10**9
    
    # 创建连续的代码块
    codes = ['RB2501'] * 150 + ['HC2501'] * 150
    
    # 基础数据
    base_data = {
        'code': codes,
        'date': timestamps,
        'change': np.random.normal(0, 1.5, n_samples)
    }
    
    # 创建多周期因子数据
    fd_dfs = {}
    
    # 短期因子 (fd_1_0) - 5个技术指标
    fd_1_0_data = base_data.copy()
    fd_1_0_data.update({
        'RSI_2': np.random.uniform(20, 80, n_samples),
        'MACD_2': np.random.normal(0, 0.8, n_samples),
        'KDJ_K_2': np.random.uniform(0, 100, n_samples),
        'KDJ_D_2': np.random.uniform(0, 100, n_samples),
        'VOL_RATIO_2': np.random.uniform(0.5, 2.0, n_samples)
    })
    fd_dfs['fd_1_0'] = pd.DataFrame(fd_1_0_data)
    
    # 中期因子 (fd_1_1) - 6个移动平均指标
    fd_1_1_data = base_data.copy()
    fd_1_1_data.update({
        'MA_5_2': np.random.normal(3500, 100, n_samples),
        'MA_10_2': np.random.normal(3500, 80, n_samples),
        'MA_20_2': np.random.normal(3500, 60, n_samples),
        'BOLL_UP_2': np.random.normal(3600, 50, n_samples),
        'BOLL_MID_2': np.random.normal(3500, 40, n_samples),
        'BOLL_DOWN_2': np.random.normal(3400, 50, n_samples)
    })
    fd_dfs['fd_1_1'] = pd.DataFrame(fd_1_1_data)
    
    # 长期因子 (fd_2_0) - 3个趋势指标
    fd_2_0_data = base_data.copy()
    fd_2_0_data.update({
        'MA_60_2': np.random.normal(3500, 150, n_samples),
        'ATR_2': np.random.uniform(10, 100, n_samples),
        'ADX_2': np.random.uniform(10, 50, n_samples)
    })
    fd_dfs['fd_2_0'] = pd.DataFrame(fd_2_0_data)
    
    # 创建标签数据
    lb_df = pd.DataFrame({
        'code': codes,
        'date': timestamps,
        'change': base_data['change'],
        'long_label': (np.array(base_data['change']) > 1.0).astype(int),
        'short_label': (np.array(base_data['change']) < -1.0).astype(int),
        'label': np.where(np.array(base_data['change']) > 1.0, 2, 
                         np.where(np.array(base_data['change']) < -1.0, 0, 1)),
        'code_encoded': [0] * 150 + [1] * 150,
        # 时间特征
        'tf0': np.random.randint(0, 24, n_samples),
        'tf1': np.random.randint(0, 7, n_samples),
        'tf2': np.random.randint(0, 31, n_samples),
        'tf3': np.random.randint(0, 12, n_samples),
        'tf4': np.random.randint(0, 4, n_samples)
    })
    
    return fd_dfs, lb_df

def demonstrate_merged_sampling():
    """演示合并采样功能"""
    print("=" * 60)
    print("DataSampler2 合并采样功能演示")
    print("=" * 60)
    
    try:
        from pyqlab.data.dataset.handler import DataSampler2, DataConfig, DirectionType
        
        # 1. 创建数据
        fd_dfs, lb_df = create_sample_data()
        
        print(f"\n1. 数据概览:")
        total_features = 0
        for period_name, period_df in fd_dfs.items():
            feature_cols = [col for col in period_df.columns 
                          if col not in ['code', 'date', 'change']]
            print(f"   {period_name}: {len(feature_cols)} 个特征")
            total_features += len(feature_cols)
        print(f"   总特征数: {total_features}")
        print(f"   数据长度: {len(lb_df)}")
        
        # 2. 配置采样器
        config = DataConfig(
            win=20,                    # 20个时间步的窗口
            step=1,                    # 步长为1
            is_filter_extreme=False,   # 关闭极端值过滤
            is_normal=False,           # 关闭归一化
            verbose=True,              # 启用详细日志
            timeenc=0                  # 启用时间编码
        )
        
        sampler = DataSampler2(config)
        
        # 3. 进行采样
        print(f"\n2. 进行多周期合并采样...")
        result = sampler.sample_data(
            fd_dfs=fd_dfs,
            lb_df=lb_df,
            direction=DirectionType.LONG_SHORT,
            win=20,
            filter_win=0
        )
        
        # 4. 分析结果
        print(f"\n3. 采样结果分析:")
        code_data, x_data, y_data, x_mark, y_mark = result
        
        print(f"   代码数据形状: {code_data.shape}")
        print(f"   特征数据形状: {x_data.shape}")
        print(f"   标签数据形状: {y_data.shape}")
        print(f"   时间标记形状: x_mark={len(x_mark)}, y_mark={len(y_mark)}")
        
        # 验证三维数组结构
        n_samples, win_size, n_features = x_data.shape
        print(f"\n4. 三维数组结构验证:")
        print(f"   样本数: {n_samples}")
        print(f"   窗口大小: {win_size}")
        print(f"   总特征数: {n_features}")
        print(f"   数据类型: {x_data.dtype}")
        
        # 5. 展示特征分布
        print(f"\n5. 特征分布分析:")
        print(f"   fd_1_0 特征范围: [0:5]   - 短期技术指标")
        print(f"   fd_1_1 特征范围: [5:11]  - 中期移动平均")
        print(f"   fd_2_0 特征范围: [11:14] - 长期趋势指标")
        
        # 6. 模型训练示例
        print(f"\n6. 模型训练示例:")
        demonstrate_model_training(x_data, y_data, code_data)
        
        return True
        
    except Exception as e:
        print(f"✗ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_model_training(x_data, y_data, code_data):
    """演示如何使用合并后的数据进行模型训练"""
    
    # 转换为PyTorch张量
    x_tensor = torch.FloatTensor(x_data)
    y_tensor = torch.FloatTensor(y_data)
    
    print(f"   转换为PyTorch张量:")
    print(f"     x_tensor形状: {x_tensor.shape}")
    print(f"     y_tensor形状: {y_tensor.shape}")
    
    # 定义一个简单的LSTM模型
    class MultiPeriodLSTM(nn.Module):
        def __init__(self, input_size, hidden_size=64, num_layers=2):
            super().__init__()
            self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
            self.fc = nn.Linear(hidden_size, 1)
            self.dropout = nn.Dropout(0.2)
            
        def forward(self, x):
            # x shape: (batch_size, seq_len, input_size)
            lstm_out, _ = self.lstm(x)
            # 取最后一个时间步的输出
            last_output = lstm_out[:, -1, :]
            output = self.dropout(last_output)
            output = self.fc(output)
            return output.squeeze()
    
    # 创建模型
    input_size = x_tensor.shape[2]  # 总特征数
    model = MultiPeriodLSTM(input_size)
    
    print(f"   创建LSTM模型:")
    print(f"     输入特征数: {input_size}")
    print(f"     模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 模拟训练过程
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 取一小批数据进行演示
    batch_size = 32
    if len(x_tensor) >= batch_size:
        x_batch = x_tensor[:batch_size]
        y_batch = y_tensor[:batch_size]
        
        # 前向传播
        model.train()
        predictions = model(x_batch)
        loss = criterion(predictions, y_batch)
        
        print(f"   模拟训练步骤:")
        print(f"     批次大小: {batch_size}")
        print(f"     预测形状: {predictions.shape}")
        print(f"     损失值: {loss.item():.4f}")
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print(f"     ✓ 反向传播完成")
    
    print(f"   ✓ 模型训练演示完成")

def demonstrate_feature_analysis():
    """演示特征分析"""
    print(f"\n7. 特征分析演示:")
    
    try:
        from pyqlab.data.dataset.handler import DataSampler2, DataConfig, DirectionType
        
        fd_dfs, lb_df = create_sample_data()
        config = DataConfig(win=10, verbose=False)
        sampler = DataSampler2(config)
        
        result = sampler.sample_data(
            fd_dfs=fd_dfs,
            lb_df=lb_df,
            direction=DirectionType.LONG_SHORT,
            win=10,
            filter_win=0
        )
        
        x_data = result[1]
        
        # 分析不同周期的特征
        print(f"   特征统计分析:")
        
        # fd_1_0: 特征 0-4 (短期)
        short_features = x_data[:, :, 0:5]
        print(f"     短期特征 (0-4): 均值={short_features.mean():.3f}, 标准差={short_features.std():.3f}")
        
        # fd_1_1: 特征 5-10 (中期)
        medium_features = x_data[:, :, 5:11]
        print(f"     中期特征 (5-10): 均值={medium_features.mean():.3f}, 标准差={medium_features.std():.3f}")
        
        # fd_2_0: 特征 11-13 (长期)
        long_features = x_data[:, :, 11:14]
        print(f"     长期特征 (11-13): 均值={long_features.mean():.3f}, 标准差={long_features.std():.3f}")
        
        print(f"   ✓ 特征分析完成")
        
    except Exception as e:
        print(f"   ✗ 特征分析失败: {e}")

if __name__ == "__main__":
    success = demonstrate_merged_sampling()
    
    if success:
        demonstrate_feature_analysis()
        
        print("\n" + "=" * 60)
        print("✓ DataSampler2 合并功能演示完成！")
        print("\n关键优势:")
        print("1. 多周期特征独立处理，保持各自特点")
        print("2. 统一的三维数组结构，便于模型训练")
        print("3. 代码、时间、标签保持一致性")
        print("4. 特征顺序固定，支持批量训练")
        print("5. 直接兼容PyTorch/TensorFlow等深度学习框架")
        print("=" * 60)
    else:
        print("\n✗ 演示失败，请检查错误信息")
