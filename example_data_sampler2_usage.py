#!/usr/bin/env python3
"""
DataSampler2 使用示例
展示如何使用新的多周期数据采样器
"""

import pandas as pd
import numpy as np
from typing import Dict

def example_usage():
    """DataSampler2使用示例"""
    print("=" * 60)
    print("DataSampler2 使用示例")
    print("=" * 60)
    
    try:
        from pyqlab.data.dataset.handler import DataSampler2, DataConfig, DirectionType, LabelThresholds
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 1. 创建配置
        print("\n1. 创建数据配置...")
        config = DataConfig(
            win=10,                    # 窗口大小
            step=1,                    # 采样步长
            is_filter_extreme=False,   # 关闭极端值过滤（示例中）
            is_normal=False,           # 关闭归一化（示例中）
            verbose=True,              # 启用详细日志
            timeenc=0,                 # 启用时间编码
            extreme_threshold=3.0      # 极端值阈值
        )
        print(f"✓ 配置创建完成: win={config.win}, step={config.step}")
        
        # 2. 模拟从数据加载器获取的多周期数据
        print("\n2. 模拟多周期因子数据...")
        
        # 创建基础时间序列数据
        n_samples = 200
        dates = pd.date_range('2024-01-01', periods=n_samples, freq='H')
        timestamps = dates.astype(np.int64) // 10**9
        
        # 创建连续的代码块（避免频繁切换）
        codes = ['RB2501'] * 100 + ['HC2501'] * 100
        
        # 基础数据
        base_data = {
            'code': codes,
            'date': timestamps,
            'change': np.random.normal(0, 1.5, n_samples)
        }
        
        # 创建多周期因子数据字典
        fd_dfs = {}
        
        # 短期因子 (fd_1_0) - 技术指标
        fd_1_0_data = base_data.copy()
        fd_1_0_data.update({
            'RSI_2': np.random.uniform(20, 80, n_samples),
            'MACD_2': np.random.normal(0, 0.8, n_samples),
            'KDJ_K_2': np.random.uniform(0, 100, n_samples),
            'VOL_RATIO_2': np.random.uniform(0.5, 2.0, n_samples)
        })
        fd_dfs['fd_1_0'] = pd.DataFrame(fd_1_0_data)
        
        # 中期因子 (fd_1_1) - 移动平均
        fd_1_1_data = base_data.copy()
        fd_1_1_data.update({
            'MA_5_2': np.random.normal(3500, 100, n_samples),
            'MA_10_2': np.random.normal(3500, 80, n_samples),
            'MA_20_2': np.random.normal(3500, 60, n_samples),
            'BOLL_UP_2': np.random.normal(3600, 50, n_samples),
            'BOLL_MID_2': np.random.normal(3500, 40, n_samples),
            'BOLL_DOWN_2': np.random.normal(3400, 50, n_samples)
        })
        fd_dfs['fd_1_1'] = pd.DataFrame(fd_1_1_data)
        
        # 长期因子 (fd_2_0) - 趋势指标
        fd_2_0_data = base_data.copy()
        fd_2_0_data.update({
            'MA_60_2': np.random.normal(3500, 150, n_samples),
            'ATR_2': np.random.uniform(10, 100, n_samples),
            'ADX_2': np.random.uniform(10, 50, n_samples)
        })
        fd_dfs['fd_2_0'] = pd.DataFrame(fd_2_0_data)
        
        print(f"✓ 创建了 {len(fd_dfs)} 个周期的因子数据:")
        for period_name, period_df in fd_dfs.items():
            feature_cols = [col for col in period_df.columns 
                          if col not in ['code', 'date', 'change']]
            print(f"  {period_name}: {len(feature_cols)} 个特征 {feature_cols}")
        
        # 3. 创建标签数据
        print("\n3. 创建标签数据...")
        lb_df = pd.DataFrame({
            'code': codes,
            'date': timestamps,
            'change': base_data['change'],
            'long_label': (np.array(base_data['change']) > 1.0).astype(int),
            'short_label': (np.array(base_data['change']) < -1.0).astype(int),
            'label': np.where(np.array(base_data['change']) > 1.0, 2, 
                             np.where(np.array(base_data['change']) < -1.0, 0, 1)),
            'code_encoded': [0] * 100 + [1] * 100,  # 对应代码编码
            # 时间特征
            'tf0': np.random.randint(0, 24, n_samples),
            'tf1': np.random.randint(0, 7, n_samples),
            'tf2': np.random.randint(0, 31, n_samples),
            'tf3': np.random.randint(0, 12, n_samples),
            'tf4': np.random.randint(0, 4, n_samples)
        })
        print(f"✓ 标签数据创建完成: {lb_df.shape}")
        
        # 4. 创建DataSampler2并进行采样
        print("\n4. 创建DataSampler2并进行多周期采样...")
        sampler = DataSampler2(config)
        
        # 常规采样
        print("\n--- 常规采样 ---")
        results = sampler.sample_data(
            fd_dfs=fd_dfs,
            lb_df=lb_df,
            direction=DirectionType.LONG_SHORT,
            win=10,
            filter_win=0
        )
        
        print(f"\n常规采样结果分析:")
        for period_name, result in results.items():
            if len(result) >= 3:
                code_data, x_data, y_data = result[0], result[1], result[2]
                print(f"  {period_name}:")
                print(f"    样本数: {len(x_data)}")
                print(f"    特征维度: {x_data.shape}")
                print(f"    窗口大小: {x_data.shape[1] if len(x_data) > 0 else 0}")
                print(f"    特征数量: {x_data.shape[2] if len(x_data) > 0 else 0}")
        
        # 过滤采样
        print("\n--- 过滤采样 ---")
        results_filtered = sampler.sample_data(
            fd_dfs=fd_dfs,
            lb_df=lb_df,
            direction=DirectionType.MULTI_LABEL,
            win=10,
            filter_win=5
        )
        
        print(f"\n过滤采样结果分析:")
        for period_name, result in results_filtered.items():
            if len(result) >= 3:
                code_data, x_data, y_data = result[0], result[1], result[2]
                print(f"  {period_name}: 样本数 {len(x_data)}")
        
        # 5. 展示数据结构特点
        print("\n5. DataSampler2 关键特点展示:")
        print("   ✓ 多周期分离: 每个周期的特征向量完全独立")
        print("   ✓ 特征维度不同: 不同周期可以有不同数量的特征")
        print("   ✓ 时间对齐: 所有周期使用相同的时间索引进行采样")
        print("   ✓ 灵活配置: 支持各种采样参数和过滤选项")
        
        # 6. 实际使用建议
        print("\n6. 实际使用建议:")
        print("   • 根据不同周期的特点设置合适的特征")
        print("   • 短期周期(fd_1_0): 技术指标、价格动量等")
        print("   • 中期周期(fd_1_1): 移动平均、布林带等")
        print("   • 长期周期(fd_2_0): 趋势指标、波动率等")
        print("   • 在模型中可以为不同周期设计不同的网络分支")
        
        return True
        
    except Exception as e:
        print(f"✗ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = example_usage()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ DataSampler2 使用示例运行成功！")
        print("\n现在您可以在实际项目中使用DataSampler2来处理多周期因子数据。")
    else:
        print("✗ 示例运行失败，请检查错误信息。")
    print("=" * 60)
