#!/usr/bin/env python3
"""
演示如何在x_data中维持周期个数x周期向量个数的维度结构
"""

import pandas as pd
import numpy as np

def create_sample_data():
    """创建示例多周期数据"""
    print("创建示例多周期数据...")
    
    # 创建时间序列
    n_samples = 200
    dates = pd.date_range('2024-01-01', periods=n_samples, freq='H')
    timestamps = dates.astype(np.int64) // 10**9
    
    # 创建连续的代码块
    codes = ['RB2501'] * 100 + ['HC2501'] * 100
    
    # 基础数据
    base_data = {
        'code': codes,
        'date': timestamps,
        'change': np.random.normal(0, 1.5, n_samples)
    }
    
    # 创建多周期因子数据
    fd_dfs = {}
    
    # 短期因子 (fd_1_0) - 5个技术指标
    fd_1_0_data = base_data.copy()
    fd_1_0_data.update({
        'RSI_2': np.random.uniform(20, 80, n_samples),
        'MACD_2': np.random.normal(0, 0.8, n_samples),
        'KDJ_K_2': np.random.uniform(0, 100, n_samples),
        'KDJ_D_2': np.random.uniform(0, 100, n_samples),
        'VOL_RATIO_2': np.random.uniform(0.5, 2.0, n_samples)
    })
    fd_1_0_df = pd.DataFrame(fd_1_0_data)
    # 只保留特征列，移除code, date, change
    fd_dfs['fd_1_0'] = fd_1_0_df[['RSI_2', 'MACD_2', 'KDJ_K_2', 'KDJ_D_2', 'VOL_RATIO_2']]

    # 中期因子 (fd_1_1) - 3个移动平均指标
    fd_1_1_data = base_data.copy()
    fd_1_1_data.update({
        'MA5_2': np.random.normal(100, 10, n_samples),
        'MA20_2': np.random.normal(100, 8, n_samples),
        'MA60_2': np.random.normal(100, 5, n_samples)
    })
    fd_1_1_df = pd.DataFrame(fd_1_1_data)
    # 只保留特征列
    fd_dfs['fd_1_1'] = fd_1_1_df[['MA5_2', 'MA20_2', 'MA60_2']]

    # 长期因子 (fd_2_0) - 2个趋势指标
    fd_2_0_data = base_data.copy()
    fd_2_0_data.update({
        'TREND_2': np.random.normal(0, 1, n_samples),
        'MOMENTUM_2': np.random.normal(0, 0.5, n_samples)
    })
    fd_2_0_df = pd.DataFrame(fd_2_0_data)
    # 只保留特征列
    fd_dfs['fd_2_0'] = fd_2_0_df[['TREND_2', 'MOMENTUM_2']]
    
    # 创建标签数据
    lb_df = pd.DataFrame(base_data)
    
    return fd_dfs, lb_df

def demonstrate_period_separation():
    """演示周期分离功能"""
    print("=" * 60)
    print("演示如何在x_data中维持周期个数x周期向量个数的维度")
    print("=" * 60)
    
    try:
        from pyqlab.data.dataset.handler import DataConfig, DirectionType
        
        # 1. 创建示例数据
        fd_dfs, lb_df = create_sample_data()
        
        print(f"\n1. 数据概览:")
        total_features = 0
        for period_name, period_df in fd_dfs.items():
            feature_cols = [col for col in period_df.columns 
                          if col not in ['code', 'date', 'change']]
            print(f"   {period_name}: {len(feature_cols)} 个特征")
            total_features += len(feature_cols)
        print(f"   总特征数: {total_features}")
        print(f"   数据长度: {len(lb_df)}")
        
        # 2. 准备标签数据（添加必要的列）
        lb_df['code_encoded'] = [0 if code == 'RB2501' else 1 for code in lb_df['code']]
        
        # 3. 直接测试采样器功能（跳过DataHandler的复杂初始化）
        print(f"\n2. 测试默认行为 (keep_period_separate=False):")

        # 创建配置
        from pyqlab.data.dataset.handler import DataConfig, DataSampler, DirectionType

        config_default = DataConfig(
            win=10,
            step=1,
            is_normal=False,
            verbose=False,
            keep_period_separate=False  # 默认行为
        )

        # 直接调用采样器
        sampler_default = DataSampler(config_default)
        result_default = sampler_default.sample_data(
            fd_dfs, lb_df, DirectionType.LONG_SHORT, 10, 0
        )

        print(f"   默认模式 x_data 形状: {result_default[1].shape}")
        print(f"   说明: (样本数={result_default[1].shape[0]}, 窗口大小={result_default[1].shape[1]}, 总特征数={result_default[1].shape[2]})")

        # 4. 测试周期分离行为 (keep_period_separate=True)
        print(f"\n3. 测试周期分离行为 (keep_period_separate=True):")

        config_separate = DataConfig(
            win=10,
            step=1,
            is_normal=False,
            verbose=False,
            keep_period_separate=True  # 启用周期分离
        )

        # 直接调用采样器
        sampler_separate = DataSampler(config_separate)
        result_separate = sampler_separate.sample_data(
            fd_dfs, lb_df, DirectionType.LONG_SHORT, 10, 0
        )
        
        print(f"   周期分离模式 x_data 形状: {result_separate[1].shape}")
        print(f"   说明: (样本数={result_separate[1].shape[0]}, 周期数={result_separate[1].shape[1]}, 窗口大小={result_separate[1].shape[2]}, 每周期特征数={result_separate[1].shape[3]})")
        
        # 5. 详细分析周期分离的数据结构
        print(f"\n4. 周期分离数据结构详细分析:")
        x_data = result_separate[1]
        period_names = list(fd_dfs.keys())
        
        for i, period_name in enumerate(period_names):
            period_features = [col for col in fd_dfs[period_name].columns 
                             if col not in ['code', 'date', 'change']]
            print(f"   周期 {i} ({period_name}): {len(period_features)} 个特征")
            print(f"      特征名称: {period_features}")
            print(f"      数据形状: x_data[:, {i}, :, :{len(period_features)}]")
            
            # 显示第一个样本的第一个时间步的数据
            if x_data.shape[0] > 0:
                sample_data = x_data[0, i, 0, :len(period_features)]
                print(f"      示例数据 (第1个样本, 第1个时间步): {sample_data}")
        
        print(f"\n5. 使用建议:")
        print(f"   - 对于传统的深度学习模型，使用默认模式 (keep_period_separate=False)")
        print(f"   - 对于需要分别处理不同周期特征的模型，使用周期分离模式 (keep_period_separate=True)")
        print(f"   - 周期分离模式便于实现多尺度特征融合和注意力机制")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保 pyqlab 模块已正确安装")
    except Exception as e:
        print(f"运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demonstrate_period_separation()
