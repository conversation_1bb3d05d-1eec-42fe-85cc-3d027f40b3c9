import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Optional, Tuple, List


class MultiScaleConvBlock(nn.Module):
    """
    多尺度卷积块，同时捕获不同时间尺度的特征
    """
    def __init__(self, in_channels, out_channels, kernel_sizes=[1, 3, 5], dropout=0.1):
        super().__init__()
        self.branches = nn.ModuleList()
        branch_out_channels = out_channels // len(kernel_sizes)

        for kernel_size in kernel_sizes:
            padding = kernel_size // 2
            branch = nn.Sequential(
                nn.Conv2d(in_channels, branch_out_channels,
                         kernel_size=(kernel_size, kernel_size),
                         padding=(padding, padding)),
                nn.BatchNorm2d(branch_out_channels),
                nn.GELU(),
                nn.Dropout2d(dropout)
            )
            self.branches.append(branch)

        # 调整输出通道数以匹配目标
        self.adjust_channels = nn.Conv2d(
            branch_out_channels * len(kernel_sizes),
            out_channels,
            kernel_size=1
        )

    def forward(self, x):
        branch_outputs = [branch(x) for branch in self.branches]
        concat_output = torch.cat(branch_outputs, dim=1)
        return self.adjust_channels(concat_output)


class ChannelAttention(nn.Module):
    """
    通道注意力机制，学习不同特征通道的重要性
    """
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.GELU(),
            nn.Linear(channels // reduction, channels, bias=False)
        )

    def forward(self, x):
        b, c, _, _ = x.size()

        # 平均池化和最大池化
        avg_out = self.fc(self.avg_pool(x).view(b, c))
        max_out = self.fc(self.max_pool(x).view(b, c))

        # 注意力权重
        attention = torch.sigmoid(avg_out + max_out).view(b, c, 1, 1)
        return x * attention


class SpatialAttention(nn.Module):
    """
    空间注意力机制，学习空间位置的重要性
    """
    def __init__(self, kernel_size=7):
        super().__init__()
        padding = kernel_size // 2
        self.conv = nn.Conv2d(2, 1, kernel_size=kernel_size, padding=padding, bias=False)

    def forward(self, x):
        # 通道维度的平均和最大
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)

        # 拼接并卷积
        concat = torch.cat([avg_out, max_out], dim=1)
        attention = torch.sigmoid(self.conv(concat))
        return x * attention


class CBAM(nn.Module):
    """
    卷积块注意力模块 (Convolutional Block Attention Module)
    结合通道注意力和空间注意力
    """
    def __init__(self, channels, reduction=16, kernel_size=7):
        super().__init__()
        self.channel_attention = ChannelAttention(channels, reduction)
        self.spatial_attention = SpatialAttention(kernel_size)

    def forward(self, x):
        x = self.channel_attention(x)
        x = self.spatial_attention(x)
        return x


class ResidualBlock(nn.Module):
    """
    残差块，包含多尺度卷积和注意力机制
    """
    def __init__(self, in_channels, out_channels, kernel_sizes=[1, 3, 5],
                 use_attention=True, dropout=0.1):
        super().__init__()

        self.conv_block = MultiScaleConvBlock(
            in_channels, out_channels, kernel_sizes, dropout
        )

        self.attention = CBAM(out_channels) if use_attention else nn.Identity()

        # 残差连接的通道调整
        self.shortcut = nn.Sequential()
        if in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        residual = self.shortcut(x)
        out = self.conv_block(x)
        out = self.attention(out)
        return F.gelu(out + residual)


class TemporalConvBlock(nn.Module):
    """
    时序卷积块，专门处理时间维度的特征
    """
    def __init__(self, in_channels, out_channels, kernel_size=3, dilation=1, dropout=0.1):
        super().__init__()
        padding = (kernel_size - 1) * dilation

        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size,
                              padding=padding, dilation=dilation)
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size,
                              padding=padding, dilation=dilation)

        self.norm1 = nn.BatchNorm1d(out_channels)
        self.norm2 = nn.BatchNorm1d(out_channels)

        self.dropout = nn.Dropout(dropout)
        self.chomp = nn.ConstantPad1d((0, -padding), 0) if padding > 0 else nn.Identity()

        # 残差连接
        self.shortcut = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()

    def forward(self, x):
        residual = self.shortcut(x)

        out = self.conv1(x)
        out = self.chomp(out)
        out = F.gelu(self.norm1(out))
        out = self.dropout(out)

        out = self.conv2(out)
        out = self.chomp(out)
        out = F.gelu(self.norm2(out))
        out = self.dropout(out)

        return F.gelu(out + residual)


class AdaptiveFeatureFusion(nn.Module):
    """
    自适应特征融合模块，动态融合不同层次的特征
    """
    def __init__(self, feature_dims: List[int], output_dim: int):
        super().__init__()
        self.feature_dims = feature_dims
        self.output_dim = output_dim

        # 特征投影层
        self.projections = nn.ModuleList([
            nn.Linear(dim, output_dim) for dim in feature_dims
        ])

        # 注意力权重计算
        self.attention_weights = nn.Sequential(
            nn.Linear(sum(feature_dims), len(feature_dims)),
            nn.Softmax(dim=-1)
        )

    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        # 投影所有特征到相同维度
        projected_features = [
            proj(feat) for proj, feat in zip(self.projections, features)
        ]

        # 计算注意力权重
        concat_features = torch.cat(features, dim=-1)
        weights = self.attention_weights(concat_features)

        # 加权融合
        fused_feature = torch.zeros_like(projected_features[0])
        for i, feat in enumerate(projected_features):
            fused_feature += weights[:, i:i+1] * feat

        return fused_feature


class TimeSeriesModel2drV2(nn.Module):
    """
    增强版时间序列模型V2，集成多项先进技术：
    1. 多尺度卷积捕获不同时间尺度特征
    2. CBAM注意力机制增强特征表达
    3. 残差连接缓解梯度消失
    4. 时序卷积处理时间依赖
    5. 自适应特征融合
    6. 概率预测和不确定性估计
    7. 多任务学习支持
    8. 高级正则化技术

    版本更新说明：
    - 适配新的数据集格式：FTSDataset现在只返回code_encoded，不再包含其他分类特征
    - 支持新旧数据格式的兼容性处理
    - 优化了输入数据的格式检查和转换逻辑
    """
    def __init__(self,
                 num_embeds=[72],
                 num_channel=5,
                 num_input=45,
                 dropout=0.5,
                 num_conv_layers=3,
                 conv_channels=None,
                 use_residual=True,
                 use_attention=True,
                 use_temporal_conv=True,
                 num_outputs=1,
                 probabilistic=False,
                 multi_task=False,
                 task_weights=None,
                 weight_decay=1e-4,
                 out_channels=(32, 64, 1152, 256),
                 ins_nums=(0, 51, 51, 8),
                 pooling="adaptive_avg",
                 inference_mode=False,
                 feature_fusion=True,
                 label_smoothing=0.0):
        super().__init__()

        self.inference_mode = inference_mode
        self.probabilistic = probabilistic
        self.multi_task = multi_task
        self.feature_fusion = feature_fusion
        self.use_temporal_conv = use_temporal_conv

        if inference_mode:
            dropout = 0.0
            probabilistic = False
            use_attention = False

        print(f"TimeSeriesModel2drV2 配置: "
              f"多尺度卷积={num_conv_layers}层, "
              f"残差连接={use_residual}, "
              f"注意力机制={use_attention}, "
              f"时序卷积={use_temporal_conv}, "
              f"特征融合={feature_fusion}, "
              f"概率预测={probabilistic}, "
              f"多任务={multi_task}")

        print(f"数据格式适配: 新版本数据集只包含code_encoded，不再包含其他分类特征")

        # 确定维度和特征 - 适配新的数据格式
        self.n_dims, n_feats = self._determine_dims(ins_nums)
        num_dims = self._calculate_num_dims(num_embeds, n_feats, ins_nums[3])

        # 设置模型配置参数
        self.use_residual = use_residual
        self.use_attention = use_attention
        self.weight_decay = weight_decay
        self.num_outputs = num_outputs
        self.label_smoothing = label_smoothing
        self.num_channel = num_channel
        self.num_input = num_input

        # 创建嵌入层
        self.embedding_layers = nn.ModuleList([
            nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i])
            for i in range(len(num_dims))
        ])

        # 设置卷积通道
        if conv_channels is None:
            # 使用更合理的通道递增策略
            base_channels = out_channels[0]
            conv_channels = [
                base_channels * (2 ** i) for i in range(num_conv_layers)
            ]

        self.conv_channels = conv_channels

        # 计算实际的输入通道数（包含嵌入特征）
        # 直接计算嵌入层的输出维度
        embed_dim = sum(num_dims) if num_dims else 0
        print(f"嵌入层配置: num_dims={num_dims}, embed_dim={embed_dim}")

        # 延迟创建卷积层，在第一次前向传播时确定实际输入通道数
        self.conv_layers = None
        self.conv_channels = conv_channels
        self.num_conv_layers = num_conv_layers
        self.use_attention = use_attention
        self.dropout = dropout

        # 存储预期的输入通道数（用于验证）
        self.expected_input_channels = num_channel + embed_dim
        print(f"预期输入通道数: {self.expected_input_channels} (原始特征: {num_channel}, 嵌入维度: {embed_dim})")

        # 卷积层将在第一次前向传播时创建

        # 时序卷积层（可选）
        if use_temporal_conv:
            self.temporal_conv_layers = nn.ModuleList()
            # 计算时序卷积的输入通道数（考虑2D到1D的转换）
            temporal_in_channels = conv_channels[-1] * self.n_dims
            for i in range(2):  # 两层时序卷积
                dilation = 2 ** i
                temporal_layer = TemporalConvBlock(
                    in_channels=temporal_in_channels if i == 0 else conv_channels[-1],
                    out_channels=conv_channels[-1],
                    kernel_size=3,
                    dilation=dilation,
                    dropout=dropout
                )
                self.temporal_conv_layers.append(temporal_layer)

        # 全局池化
        self.global_pool = self._get_pooling_layer(pooling)

        # 计算特征维度
        flat_features = conv_channels[-1]

        # 特征融合（可选）
        if feature_fusion:
            # 收集不同层的特征维度（每层卷积后的特征维度）
            feature_dims = conv_channels + [flat_features]  # 每层的通道数加上最终特征
            self.feature_fusion_module = AdaptiveFeatureFusion(
                feature_dims, flat_features
            )

        # 输出层
        linear_out_features = out_channels[3]
        self.feature_transform = nn.Sequential(
            nn.Linear(flat_features, linear_out_features),
            nn.LayerNorm(linear_out_features),
            nn.GELU(),
            nn.Dropout(dropout),
        )

        # 多任务输出层
        if multi_task:
            # 主任务：价格预测
            self.main_task_head = self._create_output_head(linear_out_features, num_outputs, probabilistic)
            # 辅助任务：方向预测
            self.aux_task_head = nn.Sequential(
                nn.Linear(linear_out_features, 64),
                nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(64, 3)  # 上涨、下跌、横盘
            )
            self.task_weights = task_weights or [1.0, 0.3]
        else:
            self.output_head = self._create_output_head(linear_out_features, num_outputs, probabilistic)

    def _create_output_head(self, input_dim, output_dim, probabilistic):
        """创建输出头"""
        if probabilistic:
            return nn.ModuleDict({
                'mean': nn.Linear(input_dim, output_dim),
                'log_var': nn.Linear(input_dim, output_dim)
            })
        else:
            return nn.Linear(input_dim, output_dim)

    def _determine_dims(self, ins_nums):
        """确定输入数据的维度"""
        if ins_nums[0] > 0 and ins_nums[1] == 0 and ins_nums[2] == 0:
            return 2, ins_nums[0]
        elif ins_nums[0] == 0 and ins_nums[1] > 0 and ins_nums[2] > 0:
            return 3, ins_nums[1]
        else:
            raise ValueError("无效的ins_nums配置")

    def _calculate_num_dims(self, num_embeds, n_feats, ins_num_3):
        """计算嵌入维度"""
        num_dims = [math.ceil(np.sqrt(num_embed)) for num_embed in num_embeds if num_embed > 0]
        dims_sum = sum(num_dims)
        if dims_sum > 0:
            num_dims = [int(num_dim / dims_sum * (n_feats - ins_num_3)) for num_dim in num_dims]

            diff = sum(num_dims) - (n_feats - ins_num_3)
            if diff != 0:
                num_dims[0] -= diff

        return num_dims

    def _get_pooling_layer(self, pooling):
        """获取池化层"""
        poolings = {
            "max": nn.AdaptiveMaxPool2d(1),
            "avg": nn.AdaptiveAvgPool2d(1),
            "adaptive_max": nn.AdaptiveMaxPool2d(1),
            "adaptive_avg": nn.AdaptiveAvgPool2d(1)
        }
        if pooling not in poolings:
            raise ValueError("池化方法必须是: " + ", ".join(poolings.keys()))
        return poolings[pooling]

    def _create_conv_layers(self, actual_input_channels):
        """动态创建卷积层"""
        if self.conv_layers is not None:
            return  # 已经创建过了

        print(f"动态创建卷积层，实际输入通道数: {actual_input_channels}")

        self.conv_layers = nn.ModuleList()
        in_channels = actual_input_channels

        for i in range(self.num_conv_layers):
            layer = ResidualBlock(
                in_channels=in_channels,
                out_channels=self.conv_channels[i],
                kernel_sizes=[1, 3, 5],
                use_attention=self.use_attention,
                dropout=self.dropout
            )
            self.conv_layers.append(layer)
            in_channels = self.conv_channels[i]

    def _embed_data(self, embds):
        """
        嵌入类别数据
        适配新的数据格式：现在embds只包含code_encoded，不再包含其他分类特征
        """
        if len(self.embedding_layers) == 0:
            return None

        embedded_data = None
        # 确保嵌入维度不超过输入维度
        num_embed_features = min(embds.shape[-1], len(self.embedding_layers))

        for i in range(num_embed_features):
            # 处理code_encoded嵌入
            category_data = self.embedding_layers[i](embds[:, :, i])
            embedded_data = category_data if embedded_data is None else torch.cat([embedded_data, category_data], dim=-1)

        return embedded_data

    def _check_input_format(self, embds, x):
        """
        检查输入数据格式并进行必要的转换
        支持新旧两种数据格式的兼容性
        """
        # 检查embds的格式
        if embds.shape[-1] > 1:
            print("警告: 检测到旧格式的嵌入数据（包含多个分类特征），将只使用code_encoded")
            # 只保留第一列（code_encoded）
            embds = embds[:, :, :1]

        # 检查x的格式
        if len(x.shape) == 3:
            if x.shape[1] == x.shape[2]:
                # 可能是 (batch, seq_len, features) 格式，需要转置
                print("检测到输入格式为 (batch, seq_len, features)，将转换为 (batch, features, seq_len)")
                x = x.transpose(1, 2)
            # 否则假设已经是正确的 (batch, features, seq_len) 格式

        return embds, x

    def forward(self, embds, x):
        """
        前向传播
        Args:
            embds: 嵌入特征输入，形状为 (batch_size, seq_len, num_embeds)
                  注意：适配新的数据格式，现在只包含code_encoded，不再包含其他分类特征
            x: 时间序列特征输入，支持两种格式：
               - 新格式：(batch_size, seq_len, num_features)
               - 旧格式：(batch_size, num_features, seq_len)
        Returns:
            根据配置返回不同格式的输出
        """
        assert len(embds.shape) > 2

        # 检查并调整输入格式以保证兼容性
        embds, x = self._check_input_format(embds, x)

        # 处理嵌入数据 - 适配新的数据格式
        if len(self.embedding_layers) > 0:
            embedded_data = self._embed_data(embds)
            if embedded_data is not None:
                # embedded_data形状: (batch_size, seq_len, embed_dim)
                # x形状: (batch_size, features, seq_len)
                # 需要确保两者在seq_len维度上匹配

                # 检查序列长度是否匹配
                if embedded_data.shape[1] != x.shape[2]:
                    print(f"警告: 嵌入数据序列长度 {embedded_data.shape[1]} 与特征数据序列长度 {x.shape[2]} 不匹配")
                    # 调整到较小的长度
                    min_len = min(embedded_data.shape[1], x.shape[2])
                    embedded_data = embedded_data[:, :min_len, :]
                    x = x[:, :, :min_len]

                # 转换嵌入数据格式以匹配x的格式
                embedded_data = embedded_data.transpose(1, 2)  # 转换为 (batch_size, embed_dim, seq_len)
                # 将嵌入数据与时间序列特征拼接
                x = torch.cat([x, embedded_data], dim=1)  # 在特征维度拼接

        # 动态创建卷积层（如果还没有创建）
        actual_input_channels = x.shape[1]
        self._create_conv_layers(actual_input_channels)

        # 调整输入形状为2D卷积格式
        # 确保最后一个维度能被n_dims整除
        seq_len = x.shape[2]
        if seq_len % self.n_dims != 0:
            # 如果不能整除，进行填充或截断
            target_len = (seq_len // self.n_dims) * self.n_dims
            if target_len == 0:
                target_len = self.n_dims
            x = x[:, :, :target_len]

        x = x.reshape(x.shape[0], x.shape[1], self.n_dims, x.shape[2] // self.n_dims)

        # 存储中间特征用于特征融合
        intermediate_features = []

        # 应用多尺度残差卷积层
        for conv_layer in self.conv_layers:
            x = conv_layer(x)
            if self.feature_fusion:
                # 全局平均池化获取特征向量
                pooled_feat = F.adaptive_avg_pool2d(x, 1).flatten(1)
                intermediate_features.append(pooled_feat)

        # 时序卷积处理（可选）
        if self.use_temporal_conv and hasattr(self, 'temporal_conv_layers'):
            # 转换为1D卷积格式 (batch, channels, time)
            b, c, h, w = x.shape
            x_1d = x.view(b, c * h, w)

            # 应用时序卷积层
            for temporal_layer in self.temporal_conv_layers:
                x_1d = temporal_layer(x_1d)

            # 转换回2D格式，注意输出通道数可能已改变
            # 计算时序卷积后的实际通道数
            new_channels = x_1d.shape[1]  # 时序卷积后的通道数

            # 根据时序卷积的设计，输出通道数应该是conv_channels[-1]
            # 而输入通道数是conv_channels[-1] * h，所以输出通道数减少了h倍
            if new_channels == c:
                # 如果输出通道数等于原始c，说明时序卷积将c*h压缩回了c
                # 我们需要重新分配到h维度
                x = x_1d.view(b, new_channels, 1, w)  # 将其reshape为(b, c, 1, w)
                x = x.expand(b, new_channels, h, w)   # 扩展h维度
            elif new_channels % h == 0:
                # 如果新通道数能被h整除，重新计算c
                new_c = new_channels // h
                x = x_1d.view(b, new_c, h, w)
            else:
                # 如果不能整除，使用自适应方法处理
                x = x_1d.view(b, new_channels, 1, w)  # 保持为(b, new_channels, 1, w)
                x = F.adaptive_avg_pool2d(x, (h, w))  # 调整到目标尺寸

        # 全局池化
        x = self.global_pool(x)
        x = x.flatten(1)

        # 特征融合（可选）
        if self.feature_fusion and intermediate_features:
            intermediate_features.append(x)
            x = self.feature_fusion_module(intermediate_features)

        # 特征变换
        x = self.feature_transform(x)

        # 输出处理
        if self.multi_task:
            return self._multi_task_output(x)
        else:
            return self._single_task_output(x)

    def _single_task_output(self, x):
        """单任务输出"""
        if self.inference_mode:
            if isinstance(self.output_head, nn.ModuleDict):
                # 概率预测但在推理模式下只返回均值
                output = self.output_head['mean'](x)
            else:
                output = self.output_head(x)
            return output.view(-1) if self.num_outputs == 1 else output
        else:
            if self.probabilistic:
                mean = self.output_head['mean'](x)
                log_var = self.output_head['log_var'](x)
                var = torch.exp(log_var) + 1e-6
                # 确保输出形状正确
                if self.num_outputs == 1:
                    mean = mean.view(-1)
                    var = var.view(-1)
                return mean, var
            else:
                output = self.output_head(x)
                return output.view(-1) if self.num_outputs == 1 else output

    def _multi_task_output(self, x):
        """多任务输出"""
        # 主任务输出
        if isinstance(self.main_task_head, nn.ModuleDict):
            main_mean = self.main_task_head['mean'](x)
            main_log_var = self.main_task_head['log_var'](x)
            main_var = torch.exp(main_log_var) + 1e-6
            # 确保输出形状正确
            if self.num_outputs == 1:
                main_mean = main_mean.view(-1)
                main_var = main_var.view(-1)
            main_output = (main_mean, main_var)
        else:
            main_output = self.main_task_head(x)
            # 确保输出形状正确
            if self.num_outputs == 1:
                main_output = main_output.view(-1)

        # 辅助任务输出
        aux_output = self.aux_task_head(x)

        if self.inference_mode:
            if isinstance(main_output, tuple):
                return main_output[0], aux_output
            else:
                return main_output, aux_output
        else:
            return main_output, aux_output

    def get_regularization_loss(self):
        """计算正则化损失"""
        reg_loss = 0.0

        # L2正则化
        if self.weight_decay > 0:
            for param in self.parameters():
                reg_loss += self.weight_decay * torch.sum(param ** 2)

        return reg_loss

    def compute_loss(self, outputs, targets, aux_targets=None):
        """
        计算损失函数
        Args:
            outputs: 模型输出
            targets: 主任务目标
            aux_targets: 辅助任务目标（可选）
        """
        total_loss = 0.0
        loss_dict = {}

        if self.multi_task:
            main_output, aux_output = outputs

            # 主任务损失
            if isinstance(main_output, tuple):
                # 概率预测损失
                mean, var = main_output
                main_loss = self._gaussian_nll_loss(mean, var, targets)
            else:
                # 确定性预测损失
                main_loss = F.mse_loss(main_output, targets)
                if self.label_smoothing > 0:
                    main_loss = self._label_smoothed_loss(main_output, targets)

            # 辅助任务损失（方向预测）
            if aux_targets is not None:
                aux_loss = F.cross_entropy(aux_output, aux_targets)
                total_loss = (self.task_weights[0] * main_loss +
                             self.task_weights[1] * aux_loss)
                loss_dict.update({
                    'main_loss': main_loss.item(),
                    'aux_loss': aux_loss.item()
                })
            else:
                total_loss = main_loss
                loss_dict['main_loss'] = main_loss.item()
        else:
            # 单任务
            if isinstance(outputs, tuple):
                # 概率预测损失
                mean, var = outputs
                total_loss = self._gaussian_nll_loss(mean, var, targets)
            else:
                # 确定性预测损失
                total_loss = F.mse_loss(outputs, targets)
                if self.label_smoothing > 0:
                    total_loss = self._label_smoothed_loss(outputs, targets)

            loss_dict['main_loss'] = total_loss.item()

        # 添加正则化损失
        reg_loss = self.get_regularization_loss()
        total_loss += reg_loss
        loss_dict['reg_loss'] = reg_loss.item()
        loss_dict['total_loss'] = total_loss.item()

        return total_loss, loss_dict

    def _gaussian_nll_loss(self, mean, var, targets):
        """高斯负对数似然损失"""
        return 0.5 * (torch.log(var) + ((targets - mean) ** 2) / var).mean()

    def _label_smoothed_loss(self, outputs, targets):
        """标签平滑损失"""
        mse_loss = F.mse_loss(outputs, targets)
        smooth_loss = F.mse_loss(outputs, torch.zeros_like(outputs))
        return (1 - self.label_smoothing) * mse_loss + self.label_smoothing * smooth_loss

    def predict_with_uncertainty(self, embds, x, n_samples=20):
        """
        使用Monte Carlo Dropout进行不确定性估计
        Args:
            embds: 嵌入特征
            x: 输入特征
            n_samples: 采样次数
        Returns:
            mean_pred: 平均预测
            uncertainty: 预测不确定性（标准差）
        """
        self.train()  # 启用dropout

        predictions = []
        with torch.no_grad():
            for _ in range(n_samples):
                if self.multi_task:
                    pred, _ = self.forward(embds, x)
                    if isinstance(pred, tuple):
                        pred = pred[0]  # 取均值
                else:
                    pred = self.forward(embds, x)
                    if isinstance(pred, tuple):
                        pred = pred[0]  # 取均值
                predictions.append(pred)

        predictions = torch.stack(predictions)
        mean_pred = predictions.mean(dim=0)
        uncertainty = predictions.std(dim=0)

        self.eval()  # 恢复评估模式
        return mean_pred, uncertainty

    def get_attention_weights(self, embds, x):
        """
        获取注意力权重用于可解释性分析
        """
        attention_weights = {}

        def hook_fn(name):
            def hook(module, input, output):
                # 忽略未使用的参数警告
                _ = input, output
                if hasattr(module, 'attention_weights'):
                    attention_weights[name] = module.attention_weights.detach()
            return hook

        # 注册钩子
        hooks = []
        for i, layer in enumerate(self.conv_layers):
            if hasattr(layer, 'attention'):
                hook = layer.attention.register_forward_hook(hook_fn(f'conv_layer_{i}'))
                hooks.append(hook)

        # 前向传播
        with torch.no_grad():
            _ = self.forward(embds, x)

        # 移除钩子
        for hook in hooks:
            hook.remove()

        return attention_weights

    def export_onnx(self, save_path, example_embds, example_x):
        """
        导出ONNX模型
        Args:
            save_path: ONNX模型保存路径
            example_embds: 示例嵌入输入
            example_x: 示例特征输入
        """
        # 切换到推理模式
        original_mode = self.inference_mode
        self.inference_mode = True
        self.eval()

        # 确保输入数据在正确的设备上
        device = next(self.parameters()).device
        example_embds = example_embds.to(device)
        example_x = example_x.to(device)

        # 设置动态轴
        dynamic_axes = {
            'embds': {0: 'batch_size'},
            'x': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }

        # 导出模型
        torch.onnx.export(
            self,
            (example_embds, example_x),
            save_path,
            input_names=['embds', 'x'],
            output_names=['output'],
            dynamic_axes=dynamic_axes,
            opset_version=11,
            do_constant_folding=True,
            verbose=False
        )
        print(f"模型已导出到: {save_path}")

        # 验证导出的模型
        try:
            import onnx
            onnx_model = onnx.load(save_path)
            onnx.checker.check_model(onnx_model)
            print("ONNX模型验证通过")
        except ImportError:
            print("警告: 未安装onnx包，跳过模型验证")
        except Exception as e:
            print(f"ONNX模型验证失败: {e}")

        # 恢复原始模式
        self.inference_mode = original_mode
        if not original_mode:
            self.train()

    @staticmethod
    def load_from_onnx(onnx_path):
        """从ONNX文件加载模型（仅用于推理）"""
        try:
            import onnxruntime as ort
            ort_session = ort.InferenceSession(onnx_path)
            print(f"已加载ONNX模型: {onnx_path}")
            return ort_session
        except ImportError:
            raise ImportError("请安装onnxruntime: pip install onnxruntime")

    def get_model_size(self):
        """获取模型大小信息"""
        param_count = sum(p.numel() for p in self.parameters())
        trainable_param_count = sum(p.numel() for p in self.parameters() if p.requires_grad)

        # 估算模型大小（MB）
        param_size = sum(p.numel() * p.element_size() for p in self.parameters())
        buffer_size = sum(b.numel() * b.element_size() for b in self.buffers())
        model_size_mb = (param_size + buffer_size) / (1024 * 1024)

        return {
            'total_params': param_count,
            'trainable_params': trainable_param_count,
            'model_size_mb': model_size_mb
        }

    def get_example_inputs(self, batch_size=1):
        """
        生成用于ONNX导出的示例输入
        适配新的数据格式：embds现在只包含code_encoded
        """
        # 生成示例嵌入输入 - 适配新格式，只包含code_encoded
        num_embed_features = len(self.embedding_layers)
        if num_embed_features > 0:
            # 新格式：(batch_size, seq_len, 1) - 只有code_encoded
            example_embds = torch.zeros(batch_size, self.num_channel, 1, dtype=torch.int64)
        else:
            example_embds = torch.zeros(batch_size, self.num_channel, 1, dtype=torch.int64)

        # 生成示例特征输入 - 适配新格式：(batch_size, seq_len, num_features)
        example_x = torch.zeros(batch_size, self.num_channel, self.num_input, dtype=torch.float32)

        return example_embds, example_x
