#!/usr/bin/env python3
"""
测试新的DataSampler2多周期数据采样器
"""

import pandas as pd
import numpy as np
from typing import Dict

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 创建时间序列
    dates = pd.date_range('2024-01-01', periods=100, freq='H')
    timestamps = dates.astype(np.int64) // 10**9  # 转换为时间戳
    
    # 创建代码列表 - 连续的代码块，避免频繁切换
    codes = ['TEST1'] * 50 + ['TEST2'] * 50
    
    # 创建基础数据框架
    base_data = {
        'code': codes,
        'date': timestamps,
        'change': np.random.normal(0, 1, 100)  # 随机变化
    }
    
    # 创建多周期因子数据
    fd_dfs = {}
    
    # 周期1: fd_1_0 - 短期因子
    fd_1_0_data = base_data.copy()
    fd_1_0_data.update({
        'RSI_2': np.random.uniform(20, 80, 100),
        'MACD_2': np.random.normal(0, 0.5, 100),
        'VOL_2': np.random.uniform(1000, 10000, 100)
    })
    fd_dfs['fd_1_0'] = pd.DataFrame(fd_1_0_data)
    
    # 周期2: fd_1_1 - 中期因子
    fd_1_1_data = base_data.copy()
    fd_1_1_data.update({
        'MA_5_2': np.random.normal(100, 10, 100),
        'MA_10_2': np.random.normal(100, 8, 100),
        'BOLL_UP_2': np.random.normal(110, 5, 100),
        'BOLL_DOWN_2': np.random.normal(90, 5, 100)
    })
    fd_dfs['fd_1_1'] = pd.DataFrame(fd_1_1_data)
    
    # 周期3: fd_2_0 - 长期因子
    fd_2_0_data = base_data.copy()
    fd_2_0_data.update({
        'MA_20_2': np.random.normal(100, 15, 100),
        'ATR_2': np.random.uniform(1, 5, 100)
    })
    fd_dfs['fd_2_0'] = pd.DataFrame(fd_2_0_data)
    
    # 创建标签数据框
    lb_df = pd.DataFrame({
        'code': codes,
        'date': timestamps,
        'change': base_data['change'],
        'long_label': (np.array(base_data['change']) > 0.5).astype(int),
        'short_label': (np.array(base_data['change']) < -0.5).astype(int),
        'label': np.where(np.array(base_data['change']) > 0.5, 2, 
                         np.where(np.array(base_data['change']) < -0.5, 0, 1)),
        'code_encoded': [0] * 50 + [1] * 50,  # 对应连续的代码块
        'tf0': np.random.randint(0, 24, 100),  # 时间特征
        'tf1': np.random.randint(0, 7, 100),
        'tf2': np.random.randint(0, 31, 100),
        'tf3': np.random.randint(0, 12, 100),
        'tf4': np.random.randint(0, 4, 100)
    })
    
    return fd_dfs, lb_df

def test_data_sampler2():
    """测试DataSampler2"""
    print("\n测试DataSampler2...")
    
    try:
        from pyqlab.data.dataset.handler import DataSampler2, DataConfig, DirectionType, LabelThresholds
        
        # 创建测试数据
        fd_dfs, lb_df = create_test_data()
        
        print(f"创建的测试数据:")
        print(f"  周期数: {len(fd_dfs)}")
        for period_name, period_df in fd_dfs.items():
            print(f"  {period_name}: {period_df.shape}, 列: {list(period_df.columns)}")
        print(f"  标签数据: {lb_df.shape}, 列: {list(lb_df.columns)}")
        
        # 创建配置
        config = DataConfig(
            win=5,
            step=1,
            is_filter_extreme=False,
            is_normal=False,
            verbose=True,
            timeenc=0,  # 启用时间编码
            extreme_threshold=3.0
        )
        
        # 创建采样器
        sampler = DataSampler2(config)
        
        print(f"\n开始多周期采样测试...")
        print(f"参数: win=5, filter_win=0, direction=LONG_SHORT")
        
        # 测试常规采样
        results = sampler.sample_data(
            fd_dfs=fd_dfs,
            lb_df=lb_df,
            direction=DirectionType.LONG_SHORT,
            win=5,
            filter_win=0
        )
        
        print(f"\n采样结果:")
        print(f"  返回的周期数: {len(results)}")
        
        for period_name, result in results.items():
            print(f"\n  周期 {period_name}:")
            print(f"    结果元组长度: {len(result)}")
            if len(result) >= 3:
                code_data, x_data, y_data = result[0], result[1], result[2]
                print(f"    code_data形状: {code_data.shape}")
                print(f"    x_data形状: {x_data.shape}")
                print(f"    y_data形状: {y_data.shape}")
                
                if len(result) == 5:  # 包含时间特征
                    x_mark, y_mark = result[3], result[4]
                    print(f"    x_mark长度: {len(x_mark)}")
                    print(f"    y_mark长度: {len(y_mark)}")
        
        print(f"\n✓ DataSampler2 常规采样测试通过")
        
        # 测试过滤采样
        print(f"\n测试过滤采样 (filter_win=3)...")
        results_filtered = sampler.sample_data(
            fd_dfs=fd_dfs,
            lb_df=lb_df,
            direction=DirectionType.MULTI_LABEL,
            win=5,
            filter_win=3
        )
        
        print(f"过滤采样结果:")
        for period_name, result in results_filtered.items():
            if len(result) >= 3:
                print(f"  {period_name}: 样本数 {len(result[1])}")
        
        print(f"✓ DataSampler2 过滤采样测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ DataSampler2测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_structure_validation():
    """测试数据结构验证"""
    print("\n测试数据结构验证...")
    
    try:
        from pyqlab.data.dataset.handler import DataSampler2, DataConfig, DirectionType
        
        config = DataConfig(win=5, verbose=False)
        sampler = DataSampler2(config)
        
        # 测试空数据
        try:
            sampler.sample_data({}, pd.DataFrame(), DirectionType.LONG, 5, 0)
            print("✗ 应该抛出空数据异常")
            return False
        except ValueError as e:
            print(f"✓ 正确捕获空数据异常: {e}")
        
        # 测试无效窗口大小
        try:
            fd_dfs, lb_df = create_test_data()
            sampler.sample_data(fd_dfs, lb_df, DirectionType.LONG, 0, 0)
            print("✗ 应该抛出无效窗口大小异常")
            return False
        except ValueError as e:
            print(f"✓ 正确捕获无效窗口大小异常: {e}")
        
        print("✓ 数据结构验证测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据结构验证测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("DataSampler2 多周期数据采样器测试")
    print("=" * 60)
    
    success = True
    
    # 运行测试
    success &= test_data_sampler2()
    success &= test_data_structure_validation()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 所有测试通过！")
        print("\nDataSampler2特点:")
        print("1. ✓ 多周期因子特征向量分开处理，不合并")
        print("2. ✓ 周期根据fd_dfs的键来决定")
        print("3. ✓ 每个周期保持独立的特征向量")
        print("4. ✓ 时间对齐，所有周期使用相同的时间索引")
        print("5. ✓ 支持常规采样和过滤采样两种模式")
        print("6. ✓ 支持极端值过滤和时间特征编码")
    else:
        print("✗ 部分测试失败")
    print("=" * 60)
