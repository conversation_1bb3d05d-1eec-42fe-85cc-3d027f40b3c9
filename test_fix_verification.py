#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复后的FTSDataset
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_setup_data_fix():
    """测试修复后的setup_data方法"""
    print("测试修复后的setup_data方法...")
    
    try:
        from pyqlab.data.dataset.dataset_fts import FTSDataset
        from pyqlab.data.dataset.handler import DataHandler
        
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0)}
        }
        
        handler = DataHandler(
            data_loader=loader_config,
            win=5,
            step=1,
            verbose=False,
            is_normal=False
        )
        
        dataset = FTSDataset(
            handler=handler,
            model_type=0,
            seq_len=5
        )
        
        # 测试setup_data方法，传入handler_kwargs
        print("调用setup_data方法...")
        dataset.setup_data(handler_kwargs={'win': 10, 'step': 2, 'verbose': True})
        
        # 验证配置是否更新
        assert dataset.handler.config.win == 10
        assert dataset.handler.config.step == 2
        assert dataset.handler.config.verbose == True
        
        print("✓ setup_data方法修复测试通过")
        return True
        
    except Exception as e:
        print(f"✗ setup_data方法修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_api_simulation():
    """模拟data_api.py中的调用方式"""
    print("\n模拟data_api.py中的调用方式...")
    
    try:
        from pyqlab.data.dataset.dataset_fts import FTSDataset
        from pyqlab.data.dataset.handler import DataHandler
        
        # 模拟data_api.py中的配置
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0)}
        }
        
        handler = DataHandler(
            data_loader=loader_config,
            win=5,
            step=1,
            verbose=False,
            is_normal=False
        )
        
        dataset = FTSDataset(
            handler=handler,
            model_type=0,
            seq_len=20
        )
        
        # 模拟data_api.py中的调用
        data_handler_config = {
            'win': 20,
            'step': 1,
            'verbose': True
        }
        
        print("调用dataset.setup_data(handler_kwargs=data_handler_config)...")
        dataset.setup_data(handler_kwargs=data_handler_config)
        
        print("✓ data_api.py调用方式模拟测试通过")
        return True
        
    except Exception as e:
        print(f"✗ data_api.py调用方式模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_both_config_methods():
    """测试两种config方法都正常工作"""
    print("\n测试两种config方法...")
    
    try:
        from pyqlab.data.dataset.dataset_fts import FTSDataset
        from pyqlab.data.dataset.handler import DataHandler
        
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0)}
        }
        
        handler = DataHandler(
            data_loader=loader_config,
            win=5,
            step=1,
            verbose=False,
            is_normal=False
        )
        
        dataset = FTSDataset(
            handler=handler,
            model_type=0,
            seq_len=5
        )
        
        # 测试config方法
        print("测试config方法...")
        dataset.config(handler_kwargs={'win': 8})
        assert dataset.handler.config.win == 8
        
        # 测试setup_data方法
        print("测试setup_data方法...")
        dataset.setup_data(handler_kwargs={'win': 12, 'step': 3})
        assert dataset.handler.config.win == 12
        assert dataset.handler.config.step == 3
        
        print("✓ 两种config方法测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 两种config方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始验证修复后的FTSDataset...")
    
    tests = [
        test_setup_data_fix,
        test_data_api_simulation,
        test_both_config_methods
    ]
    
    success_count = 0
    for test_func in tests:
        if test_func():
            success_count += 1
    
    print(f"\n验证结果: {success_count}/{len(tests)} 个测试通过")
    
    if success_count == len(tests):
        print("🎉 修复成功！FTSDataset现在可以正常工作了")
    else:
        print("❌ 仍有问题需要解决")
