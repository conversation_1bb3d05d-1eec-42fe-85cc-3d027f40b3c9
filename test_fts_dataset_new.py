#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改写后的FTSDataset
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fts_dataset_basic():
    """测试FTSDataset基本功能"""
    print("测试FTSDataset基本功能...")
    
    try:
        from pyqlab.data.dataset.dataset_fts import FTSDataset
        from pyqlab.data.dataset.handler import DataHandler
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 创建数据加载器
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0), (1,1)}
        }
        
        # 创建DataHandler
        handler = DataHandler(
            data_loader=loader_config,
            win=10,
            step=1,
            verbose=False,
            main_fd_name='fd_1_0',
            is_normal=False  # 关闭归一化避免需要统计文件
        )
        
        # 创建FTSDataset - model_type=0 (MLP/CNN/TCN)
        dataset_mlp = FTSDataset(
            handler=handler,
            model_type=0,
            seq_len=10,
            label_len=0,
            pred_len=1
        )
        
        print(f"FTSDataset创建成功 (model_type=0)")
        print(f"seq_len: {dataset_mlp.seq_len}, pred_len: {dataset_mlp.pred_len}")
        
        # 创建FTSDataset - model_type=1 (Transformer/GPT)
        dataset_transformer = FTSDataset(
            handler=handler,
            model_type=1,
            seq_len=10,
            label_len=2,
            pred_len=1
        )
        
        print(f"FTSDataset创建成功 (model_type=1)")
        print(f"seq_len: {dataset_transformer.seq_len}, label_len: {dataset_transformer.label_len}, pred_len: {dataset_transformer.pred_len}")
        
        print("✓ FTSDataset基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ FTSDataset基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fts_dataset_methods():
    """测试FTSDataset方法"""
    print("\n测试FTSDataset方法...")
    
    try:
        from pyqlab.data.dataset.dataset_fts import FTSDataset
        from pyqlab.data.dataset.handler import DataHandler
        
        # 创建数据加载器
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0)}
        }
        
        # 创建DataHandler
        handler = DataHandler(
            data_loader=loader_config,
            win=5,
            step=1,
            verbose=False,
            is_normal=False
        )
        
        # 创建FTSDataset
        dataset = FTSDataset(
            handler=handler,
            model_type=0,
            seq_len=5
        )
        
        # 测试方法调用
        print("测试config方法...")
        dataset.config()
        
        print("测试get_ins_nums方法...")
        ins_nums = dataset.get_ins_nums()
        print(f"ins_nums: {ins_nums}")
        
        print("测试save_model_inputs_config方法...")
        # 这个方法需要数据准备好才能调用，先跳过
        
        print("测试__len__方法...")
        length = len(dataset)
        print(f"数据集长度: {length}")
        
        print("✓ FTSDataset方法测试通过")
        return True
        
    except Exception as e:
        print(f"✗ FTSDataset方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fts_dataset_compatibility():
    """测试FTSDataset兼容性"""
    print("\n测试FTSDataset兼容性...")
    
    try:
        from pyqlab.data.dataset.dataset_fts import FTSDataset
        from pyqlab.data.dataset.handler import DataHandler
        
        # 测试原有的初始化方式
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0)}
        }
        
        handler = DataHandler(
            data_loader=loader_config,
            win=5,
            step=1,
            verbose=False,
            is_normal=False
        )
        
        # 测试不同的初始化参数
        dataset1 = FTSDataset(handler, model_type=0, seq_len=10)
        dataset2 = FTSDataset(handler, model_type=1, seq_len=20, pred_len=5)
        
        print(f"dataset1: model_type={dataset1.model_type}, seq_len={dataset1.seq_len}")
        print(f"dataset2: model_type={dataset2.model_type}, seq_len={dataset2.seq_len}, pred_len={dataset2.pred_len}")
        
        # 验证属性设置
        assert dataset1.model_type == 0
        assert dataset1.seq_len == 10
        assert dataset1.pred_len == 0  # model_type=0时pred_len应该为0
        
        assert dataset2.model_type == 1
        assert dataset2.seq_len == 20
        assert dataset2.pred_len == 5
        
        print("✓ FTSDataset兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ FTSDataset兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试改写后的FTSDataset...")
    
    success_count = 0
    total_tests = 3
    
    if test_fts_dataset_basic():
        success_count += 1
    
    if test_fts_dataset_methods():
        success_count += 1
        
    if test_fts_dataset_compatibility():
        success_count += 1
    
    print(f"\n测试完成: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！FTSDataset改写成功")
    else:
        print("❌ 部分测试失败，需要进一步调试")
