#!/usr/bin/env python3
"""
测试 FTSDataset 中的周期分离功能
"""

def test_period_separation_in_dataset():
    """测试 FTSDataset 中的周期分离功能"""
    print("=" * 60)
    print("测试 FTSDataset 中的周期分离功能")
    print("=" * 60)
    
    try:
        from pyqlab.data.data_api import get_dataset
        
        # 1. 测试默认模式 (keep_period_separate=False)
        print("\n1. 测试默认模式 (keep_period_separate=False):")
        dataset_default = get_dataset(
            years=[2025],
            is_normal=False,
            keep_period_separate=False,  # 默认模式
            verbose=False,
            seq_len=15
        )
        
        print(f"数据集长度: {len(dataset_default)}")
        if len(dataset_default) > 0:
            code, x, y = next(iter(dataset_default))
            print(f"默认模式数据形状: code={code.shape}, x={x.shape}, y={y.shape}")
            print(f"x 的维度数: {len(x.shape)}")
        
        # 2. 测试周期分离模式 (keep_period_separate=True)
        print("\n2. 测试周期分离模式 (keep_period_separate=True):")
        dataset_separate = get_dataset(
            years=[2025],
            is_normal=False,
            keep_period_separate=True,   # 周期分离模式
            verbose=False,
            seq_len=15
        )
        
        print(f"数据集长度: {len(dataset_separate)}")
        if len(dataset_separate) > 0:
            code, x, y = next(iter(dataset_separate))
            print(f"周期分离模式数据形状: code={code.shape}, x={x.shape}, y={y.shape}")
            print(f"x 的维度数: {len(x.shape)}")
            
            if len(x.shape) == 3:
                print(f"周期分离详细信息:")
                print(f"  - 周期数: {x.shape[0]}")
                print(f"  - 窗口大小: {x.shape[1]}")
                print(f"  - 每周期最大特征数: {x.shape[2]}")
                
                # 显示每个周期的数据
                for period_idx in range(x.shape[0]):
                    period_data = x[period_idx]  # (窗口大小, 特征数)
                    print(f"  - 周期 {period_idx}: 形状 {period_data.shape}")
                    # 显示第一个时间步的特征值（非零的）
                    first_step = period_data[0]  # 第一个时间步
                    non_zero_features = (first_step != 0).sum().item()
                    print(f"    第一个时间步非零特征数: {non_zero_features}")
        
        # 3. 比较两种模式的差异
        print(f"\n3. 两种模式对比:")
        print(f"默认模式: 所有周期特征合并到一个维度")
        print(f"周期分离模式: 保持周期维度独立，便于多尺度处理")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_period_data():
    """测试多周期数据的配置"""
    print("\n" + "=" * 60)
    print("测试多周期数据配置")
    print("=" * 60)
    
    try:
        # 修改 data_api.py 中的配置以使用多个周期
        from pyqlab.data import data_api
        
        # 临时修改配置
        original_fd_set = data_api.data_handler_config["data_loader"]["fd_set"]
        print(f"原始 fd_set: {original_fd_set}")
        
        # 设置多周期
        data_api.data_handler_config["data_loader"]["fd_set"] = {(1,0), (1,1), (2,0)}
        print(f"修改后 fd_set: {data_api.data_handler_config['data_loader']['fd_set']}")
        
        # 测试多周期数据
        dataset = data_api.get_dataset(
            years=[2025],
            is_normal=False,
            keep_period_separate=True,
            verbose=False,
            seq_len=15
        )
        
        print(f"多周期数据集长度: {len(dataset)}")
        if len(dataset) > 0:
            code, x, y = next(iter(dataset))
            print(f"多周期数据形状: code={code.shape}, x={x.shape}, y={y.shape}")
            
            if len(x.shape) == 3:
                print(f"多周期详细信息:")
                print(f"  - 周期数: {x.shape[0]}")
                print(f"  - 窗口大小: {x.shape[1]}")
                print(f"  - 每周期最大特征数: {x.shape[2]}")
        
        # 恢复原始配置
        data_api.data_handler_config["data_loader"]["fd_set"] = original_fd_set
        
        return True
        
    except Exception as e:
        print(f"多周期测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试周期分离功能...")
    
    # 测试基本的周期分离功能
    success1 = test_period_separation_in_dataset()
    
    # 测试多周期数据
    success2 = test_multi_period_data()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！")
        print("\n使用建议:")
        print("1. 对于传统模型，使用 keep_period_separate=False")
        print("2. 对于需要多尺度处理的模型，使用 keep_period_separate=True")
        print("3. 周期分离模式返回 (周期数, 窗口大小, 特征数) 的三维张量")
        print("4. 可以通过 x[period_idx] 访问特定周期的数据")
    else:
        print("\n❌ 部分测试失败，请检查配置和数据")
